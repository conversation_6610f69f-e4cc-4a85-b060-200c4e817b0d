// Modern, professional, and minimalistic assessment results page
export function createBriefResultPage() {
  return '' +
    '<div class="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">' +
      // Minimal Navigation
      '<nav class="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">' +
        '<div class="max-w-6xl mx-auto px-6 py-4">' +
          '<div class="flex items-center justify-between">' +
            '<div class="flex items-center space-x-4">' +
              '<button onclick="navigateTo(\'dashboard\')" class="flex items-center text-gray-600 hover:text-gray-900 transition-colors">' +
                '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>' +
                '</svg>' +
                'Back' +
              '</button>' +
              '<h1 class="text-xl font-semibold text-gray-900">Assessment Results</h1>' +
            '</div>' +
            '<div class="flex items-center space-x-3">' +
              '<button onclick="downloadResult()" class="bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium">' +
                'Download' +
              '</button>' +
              '<button onclick="shareResult()" class="text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors">' +
                '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>' +
                '</svg>' +
              '</button>' +
            '</div>' +
          '</div>' +
        '</div>' +
      '</nav>' +

      // Main Content
      '<main class="max-w-6xl mx-auto px-6 py-12">' +
        // Hero Section - Completion
        '<div class="text-center mb-16">' +
          '<div class="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">' +
            '<svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>' +
            '</svg>' +
          '</div>' +
          '<h2 class="text-4xl font-bold text-gray-900 mb-4">Assessment Complete</h2>' +
          '<p class="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">' +
            'Congratulations! Your comprehensive psychometric assessment is complete. ' +
            'Explore your results to discover your potential and strengths.' +
          '</p>' +
          '<div class="mt-6 inline-flex items-center px-4 py-2 bg-white rounded-full shadow-sm border border-gray-200">' +
            '<svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>' +
            '</svg>' +
            '<span class="text-sm text-gray-600">Completed: <span id="completion-date" class="font-medium"></span></span>' +
          '</div>' +
        '</div>' +

        // Persona Card
        '<div class="bg-white rounded-2xl shadow-sm border border-gray-200/50 p-8 mb-12">' +
          '<div class="flex items-start space-x-6">' +
            '<div class="flex-shrink-0">' +
              '<div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center">' +
                '<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>' +
                '</svg>' +
              '</div>' +
            '</div>' +
            '<div class="flex-1">' +
              '<h3 class="text-2xl font-bold text-gray-900 mb-2" id="archetype">The Analytical Innovator</h3>' +
              '<p class="text-gray-600 text-lg leading-relaxed mb-4" id="short-summary">' +
                'You are an analytical thinker with strong investigative tendencies and high creativity.' +
              '</p>' +
              '<div class="inline-flex items-center px-3 py-1 bg-gray-100 rounded-full">' +
                '<span class="text-sm text-gray-600">Risk Tolerance: </span>' +
                '<span id="risk-tolerance" class="text-sm font-medium text-gray-900 ml-1">Moderate</span>' +
              '</div>' +
            '</div>' +
          '</div>' +
        '</div>' +

        // Assessment Overview - Grid Layout
        '<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">' +
          // VIA-IS Card
          '<div class="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6 group hover:shadow-md transition-shadow">' +
            '<div class="flex items-center justify-between mb-4">' +
              '<div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">' +
                '<svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>' +
                '</svg>' +
              '</div>' +
            '</div>' +
            '<h4 class="text-lg font-semibold text-gray-900 mb-2">Character Strengths</h4>' +
            '<p class="text-sm text-gray-600 mb-4">24 character strengths across 6 virtue categories</p>' +
            '<div class="space-y-2" id="via-is-preview"></div>' +
          '</div>' +

          // RIASEC Card
          '<div class="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6 group hover:shadow-md transition-shadow">' +
            '<div class="flex items-center justify-between mb-4">' +
              '<div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">' +
                '<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>' +
                '</svg>' +
              '</div>' +
            '</div>' +
            '<h4 class="text-lg font-semibold text-gray-900 mb-2">Career Interests</h4>' +
            '<p class="text-sm text-gray-600 mb-4">6 work personality types and career interests</p>' +
            '<div class="space-y-2" id="riasec-preview"></div>' +
          '</div>' +

          // OCEAN Card
          '<div class="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6 group hover:shadow-md transition-shadow">' +
            '<div class="flex items-center justify-between mb-4">' +
              '<div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">' +
                '<svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>' +
                '</svg>' +
              '</div>' +
            '</div>' +
            '<h4 class="text-lg font-semibold text-gray-900 mb-2">Personality</h4>' +
            '<p class="text-sm text-gray-600 mb-4">5 universal personality dimensions</p>' +
            '<div class="space-y-2" id="ocean-preview"></div>' +
          '</div>' +
        '</div>' +

        // Progress Journey - Simplified
        '<div class="bg-white rounded-2xl shadow-sm border border-gray-200/50 p-8 mb-12">' +
          '<h3 class="text-2xl font-semibold text-gray-900 mb-8 text-center">Explore Your Results</h3>' +
          '<div class="flex items-center justify-between mb-8">' +
            // Step 1 - Active
            '<div class="flex flex-col items-center text-center">' +
              '<div class="w-12 h-12 bg-gray-900 text-white rounded-full flex items-center justify-center font-semibold mb-3">' +
                '1' +
              '</div>' +
              '<div class="text-sm font-medium text-gray-900">Overview</div>' +
              '<div class="text-xs text-gray-500">Current</div>' +
            '</div>' +
            '<div class="flex-1 h-px bg-gray-200 mx-4"></div>' +
            
            // Step 2
            '<div class="flex flex-col items-center text-center">' +
              '<div class="w-12 h-12 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center font-semibold mb-3">' +
                '2' +
              '</div>' +
              '<div class="text-sm font-medium text-gray-600">Strengths</div>' +
              '<div class="text-xs text-gray-400">VIA-IS</div>' +
            '</div>' +
            '<div class="flex-1 h-px bg-gray-200 mx-4"></div>' +
            
            // Step 3
            '<div class="flex flex-col items-center text-center">' +
              '<div class="w-12 h-12 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center font-semibold mb-3">' +
                '3' +
              '</div>' +
              '<div class="text-sm font-medium text-gray-600">Interests</div>' +
              '<div class="text-xs text-gray-400">RIASEC</div>' +
            '</div>' +
            '<div class="flex-1 h-px bg-gray-200 mx-4"></div>' +
            
            // Step 4
            '<div class="flex flex-col items-center text-center">' +
              '<div class="w-12 h-12 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center font-semibold mb-3">' +
                '4' +
              '</div>' +
              '<div class="text-sm font-medium text-gray-600">Personality</div>' +
              '<div class="text-xs text-gray-400">OCEAN</div>' +
            '</div>' +
            '<div class="flex-1 h-px bg-gray-200 mx-4"></div>' +
            
            // Step 5
            '<div class="flex flex-col items-center text-center">' +
              '<div class="w-12 h-12 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center font-semibold mb-3">' +
                '5' +
              '</div>' +
              '<div class="text-sm font-medium text-gray-600">Profile</div>' +
              '<div class="text-xs text-gray-400">Complete</div>' +
            '</div>' +
          '</div>' +
          
          '<div class="text-center">' +
            '<p class="text-gray-600 mb-6 max-w-2xl mx-auto">' +
              'Follow the exploration journey to understand your assessment results in depth. ' +
              'Each stage provides valuable insights for personal and career development.' +
            '</p>' +
            '<button onclick="navigateTo(\'result-via-is\')" ' +
              'class="bg-gray-900 text-white py-3 px-8 rounded-xl hover:bg-gray-800 transition-colors text-lg font-medium">' +
              'Start Exploration' +
            '</button>' +
          '</div>' +
        '</div>' +

        // Quick Actions - Simplified Grid
        '<div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">' +
          // Quick Access
          '<div class="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6">' +
            '<h4 class="text-lg font-semibold text-gray-900 mb-6">Quick Access</h4>' +
            '<div class="space-y-3">' +
              '<button onclick="navigateTo(\'result-via-is\')" ' +
                'class="w-full text-left p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all group">' +
                '<div class="font-medium text-gray-900 group-hover:text-purple-900">Character Strengths</div>' +
                '<div class="text-sm text-gray-600 group-hover:text-purple-700 mt-1">Explore your 24 character strengths</div>' +
              '</button>' +
              '<button onclick="navigateTo(\'result-riasec\')" ' +
                'class="w-full text-left p-4 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all group">' +
                '<div class="font-medium text-gray-900 group-hover:text-blue-900">Career Interests</div>' +
                '<div class="text-sm text-gray-600 group-hover:text-blue-700 mt-1">Discover your work personality type</div>' +
              '</button>' +
              '<button onclick="navigateTo(\'result-ocean\')" ' +
                'class="w-full text-left p-4 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all group">' +
                '<div class="font-medium text-gray-900 group-hover:text-green-900">Personality Traits</div>' +
                '<div class="text-sm text-gray-600 group-hover:text-green-700 mt-1">Understand your 5 personality dimensions</div>' +
              '</button>' +
            '</div>' +
          '</div>' +

          // Next Steps
          '<div class="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6">' +
            '<h4 class="text-lg font-semibold text-gray-900 mb-6">Next Steps</h4>' +
            '<div class="space-y-6">' +
              '<div class="flex items-start space-x-4">' +
                '<div class="flex-shrink-0 w-8 h-8 bg-green-100 text-green-700 rounded-full flex items-center justify-center text-sm font-semibold mt-0.5">' +
                  '1' +
                '</div>' +
                '<div>' +
                  '<div class="font-medium text-gray-900">Explore Detailed Results</div>' +
                  '<div class="text-sm text-gray-600 mt-1">Follow the exploration journey for deep insights</div>' +
                '</div>' +
              '</div>' +
              '<div class="flex items-start space-x-4">' +
                '<div class="flex-shrink-0 w-8 h-8 bg-green-100 text-green-700 rounded-full flex items-center justify-center text-sm font-semibold mt-0.5">' +
                  '2' +
                '</div>' +
                '<div>' +
                  '<div class="font-medium text-gray-900">Create Development Plan</div>' +
                  '<div class="text-sm text-gray-600 mt-1">Use insights to plan your personal development</div>' +
                '</div>' +
              '</div>' +
              '<div class="flex items-start space-x-4">' +
                '<div class="flex-shrink-0 w-8 h-8 bg-green-100 text-green-700 rounded-full flex items-center justify-center text-sm font-semibold mt-0.5">' +
                  '3' +
                '</div>' +
                '<div>' +
                  '<div class="font-medium text-gray-900">Expert Consultation</div>' +
                  '<div class="text-sm text-gray-600 mt-1">Discuss results with career counselor</div>' +
                '</div>' +
              '</div>' +
            '</div>' +
            '<button onclick="scheduleConsultation()" ' +
              'class="w-full mt-6 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium">' +
              'Schedule Consultation' +
            '</button>' +
          '</div>' +
        '</div>' +

        // Action Buttons - Bottom
        '<div class="flex flex-col sm:flex-row gap-4">' +
          '<button onclick="retakeAssessment()" ' +
            'class="flex-1 bg-white border border-gray-300 text-gray-700 py-3 px-6 rounded-xl hover:bg-gray-50 transition-colors font-medium">' +
            'Retake Assessment' +
          '</button>' +
          '<button onclick="navigateTo(\'result-via-is\')" ' +
            'class="flex-1 bg-gray-900 text-white py-3 px-6 rounded-xl hover:bg-gray-800 transition-colors font-medium">' +
            'Start Exploration' +
          '</button>' +
        '</div>' +
      '</main>' +
    '</div>';
}

// Initialization function (unchanged)
export function initBriefResult() {
  const completionDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  const dateElement = document.getElementById('completion-date');
  if (dateElement) {
    dateElement.textContent = completionDate;
  }
  loadBriefResults();
}

// Load and display functions (unchanged functionality, updated styling)
function loadBriefResults() {
  try {
    const savedProfile = localStorage.getItem('personaProfile');
    const profile = savedProfile ? JSON.parse(savedProfile) : getPersonaProfileExample();
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleAssessmentResults();
    console.log('Brief results loaded:', { profile, assessmentResults });
    displayBriefResults(profile, assessmentResults);
  } catch (error) {
    console.error('Error loading brief results:', error);
    displayBriefResults(getPersonaProfileExample(), getSampleAssessmentResults());
  }
}

function displayBriefResults(profile, assessmentResults) {
  const archetypeElement = document.getElementById('archetype');
  const shortSummaryElement = document.getElementById('short-summary');
  const riskToleranceElement = document.getElementById('risk-tolerance');
  
  if (archetypeElement) archetypeElement.textContent = profile.archetype;
  if (shortSummaryElement) shortSummaryElement.textContent = profile.shortSummary;
  if (riskToleranceElement) riskToleranceElement.textContent = profile.riskTolerance;
  
  displayViaIsPreview(assessmentResults.viaIs);
  displayRiasecPreview(assessmentResults.riasec);
  displayOceanPreview(assessmentResults.ocean);
}

function displayViaIsPreview(viaIsData) {
  const container = document.getElementById('via-is-preview');
  if (!container) return;
  
  const topStrengths = Object.entries(viaIsData)
    .map(function(entry) {
      var key = entry[0];
      var value = entry[1];
      return { key: key, value: value, name: getViaIsDisplayName(key) };
    })
    .sort(function(a, b) { return b.value - a.value; })
    .slice(0, 3);
  
  var html = '';
  for (var i = 0; i < topStrengths.length; i++) {
    var strength = topStrengths[i];
    html += '<div class="flex justify-between items-center py-2">' +
      '<span class="text-sm text-gray-700 font-medium">' + strength.name + '</span>' +
      '<span class="text-sm font-semibold text-purple-600">' + strength.value + '%</span>' +
      '</div>';
  }
  container.innerHTML = html;
}

function displayRiasecPreview(riasecData) {
  const container = document.getElementById('riasec-preview');
  if (!container) return;
  
  const topTypes = Object.entries(riasecData)
    .map(function(entry) {
      var key = entry[0];
      var value = entry[1];
      return { key: key, value: value, name: getRiasecDisplayName(key) };
    })
    .sort(function(a, b) { return b.value - a.value; })
    .slice(0, 3);
  
  var html = '';
  for (var i = 0; i < topTypes.length; i++) {
    var type = topTypes[i];
    html += '<div class="flex justify-between items-center py-2">' +
      '<span class="text-sm text-gray-700 font-medium">' + type.name + '</span>' +
      '<span class="text-sm font-semibold text-blue-600">' + type.value + '%</span>' +
      '</div>';
  }
  container.innerHTML = html;
}

function displayOceanPreview(oceanData) {
  const container = document.getElementById('ocean-preview');
  if (!container) return;
  
  const topDimensions = Object.entries(oceanData)
    .map(function(entry) {
      var key = entry[0];
      var value = entry[1];
      return { key: key, value: value, name: getOceanDisplayName(key) };
    })
    .sort(function(a, b) { return b.value - a.value; })
    .slice(0, 3);
  
  var html = '';
  for (var i = 0; i < topDimensions.length; i++) {
    var dimension = topDimensions[i];
    html += '<div class="flex justify-between items-center py-2">' +
      '<span class="text-sm text-gray-700 font-medium">' + dimension.name + '</span>' +
      '<span class="text-sm font-semibold text-green-600">' + dimension.value + '%</span>' +
      '</div>';
  }
  container.innerHTML = html;
}

// Helper functions (unchanged)
function getViaIsDisplayName(key) {
  const displayNames = {
    creativity: 'Creativity',
    curiosity: 'Curiosity',
    judgment: 'Judgment',
    loveOfLearning: 'Love of Learning',
    perspective: 'Perspective',
    bravery: 'Bravery',
    perseverance: 'Perseverance',
    honesty: 'Honesty',
    zest: 'Zest',
    love: 'Love',
    kindness: 'Kindness',
    socialIntelligence: 'Social Intelligence',
    teamwork: 'Teamwork',
    fairness: 'Fairness',
    leadership: 'Leadership',
    forgiveness: 'Forgiveness',
    humility: 'Humility',
    prudence: 'Prudence',
    selfRegulation: 'Self-Regulation',
    appreciationOfBeauty: 'Appreciation of Beauty',
    gratitude: 'Gratitude',
    hope: 'Hope',
    humor: 'Humor',
    spirituality: 'Spirituality'
  };
  return displayNames[key] || key;
}

function getRiasecDisplayName(key) {
  const displayNames = {
    realistic: 'Realistic',
    investigative: 'Investigative',
    artistic: 'Artistic',
    social: 'Social',
    enterprising: 'Enterprising',
    conventional: 'Conventional'
  };
  return displayNames[key] || key;
}

function getOceanDisplayName(key) {
  const displayNames = {
    openness: 'Openness',
    conscientiousness: 'Conscientiousness',
    extraversion: 'Extraversion',
    agreeableness: 'Agreeableness',
    neuroticism: 'Neuroticism'
  };
  return displayNames[key] || key;
}

function getPersonaProfileExample() {
  return {
    archetype: "The Analytical Innovator",
    shortSummary: "You are an analytical thinker with strong investigative tendencies and high creativity. The combination of logical-mathematical intelligence and openness to new experiences makes you excel at solving complex problems with innovative approaches.",
    riskTolerance: "Moderate"
  };
}

function getSampleAssessmentResults() {
  return {
    ocean: {
      openness: 85,
      conscientiousness: 78,
      extraversion: 45,
      agreeableness: 62,
      neuroticism: 35
    },
    riasec: {
      realistic: 25,
      investigative: 92,
      artistic: 78,
      social: 45,
      enterprising: 55,
      conventional: 68
    },
    viaIs: {
      creativity: 88,
      curiosity: 92,
      judgment: 85,
      loveOfLearning: 90,
      perspective: 82,
      bravery: 65,
      perseverance: 78,
      honesty: 75,
      zest: 58,
      love: 62,
      kindness: 68,
      socialIntelligence: 55,
      teamwork: 48,
      fairness: 72,
      leadership: 65,
      forgiveness: 58,
      humility: 62,
      prudence: 85,
      selfRegulation: 75,
      appreciationOfBeauty: 82,
      gratitude: 68,
      hope: 72,
      humor: 55,
      spirituality: 45
    }
  };
}

// Action functions (unchanged)
export function downloadResult() {
  alert('PDF download feature coming soon');
}

export function shareResult() {
  if (navigator.share) {
    navigator.share({
      title: 'My Assessment Results',
      text: 'Check out my talent assessment results',
      url: window.location.href
    });
  } else {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
      alert('Assessment results link copied to clipboard');
    });
  }
}

export function retakeAssessment() {
  if (confirm('Are you sure you want to retake the assessment? Previous results will be overwritten.')) {
    // Clear all assessment data including persona profile
    localStorage.removeItem('assessmentAnswers');
    localStorage.removeItem('assessmentCompleted');
    localStorage.removeItem('assessmentResultReady');
    localStorage.removeItem('assessment3PhaseAnswers');
    localStorage.removeItem('assessment3PhaseCompleted');
    localStorage.removeItem('assessment3PhaseState');
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('lastSaveResult');
    localStorage.removeItem('lastAssessmentId');
    localStorage.removeItem('personaProfile');
    // Navigate to assessment
    navigateTo('assessment');
  }
}

export function scheduleConsultation() {
  const personaProfile = JSON.parse(localStorage.getItem('personaProfile') || '{}');
  const archetype = personaProfile.archetype || 'Unknown';
  alert('Consultation scheduling feature coming soon.\n' +
        'Based on your "' + archetype + '" profile, we will recommend the right counselor for you.');
}