// Import utilities for VIA-IS assessment details and charts
import { VIA_IS_CATEGORIES, VIA_IS_STRENGTH_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultViaIsPage() {
  return `
    <div class="min-h-screen light-theme">
      <!-- Premium Navigation -->
      <header class="nav-glass sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-6">
              <button onclick="navigateTo('result')"
                class="flex items-center text-gray-600 hover:text-gray-900 transition-all duration-300 group">
                <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                </svg>
                <span class="font-medium">Back to Summary</span>
              </button>
              <div class="h-6 w-px bg-gray-300"></div>
              <h1 class="text-xl font-semibold text-gray-900">Character Strengths</h1>
            </div>
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-2 text-sm text-gray-500">
                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span>Page 2 of 5</span>
              </div>
              <button onclick="navigateTo('result-riasec')"
                class="btn-primary">
                Next: RIASEC
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Modern Progress Indicator -->
      <div class="glass-card-light border-b-0 rounded-none">
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex items-center justify-between mb-3">
            <span class="text-sm font-semibold text-gray-700">Assessment Progress</span>
            <span class="text-sm text-gray-600 font-medium">40% Complete</span>
          </div>
          <div class="progress-bar-modern">
            <div class="progress-fill-modern" style="width: 40%"></div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <main class="max-w-7xl mx-auto py-12 px-6">

        <!-- Hero Section -->
        <section class="mb-12">
          <div class="glass-card-light p-10 relative overflow-hidden">
            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 via-indigo-500 to-blue-500"></div>
            <div class="flex items-start space-x-8">
              <div class="flex-shrink-0">
                <div class="w-20 h-20 bg-gradient-to-br from-purple-500 via-indigo-600 to-blue-600 rounded-3xl flex items-center justify-center shadow-2xl animate-glow">
                  <span class="text-3xl">💎</span>
                </div>
              </div>
              <div class="flex-1">
                <h1 class="heading-secondary mb-4">VIA-IS Character Strengths Assessment</h1>
                <p class="text-premium text-lg leading-relaxed mb-6">
                  Values in Action Inventory identifies your 24 character strengths across 6 universal virtue categories,
                  providing a comprehensive foundation for personal excellence and optimal development.
                </p>
                <div class="flex items-center space-x-6 text-sm text-gray-500">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span class="font-medium">Scientifically Validated</span>
                  </div>
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                    </svg>
                    <span class="font-medium">Globally Recognized</span>
                  </div>
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                    </svg>
                    <span class="font-medium">Research-Based</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Executive Summary -->
        <section class="mb-8">
          <div class="bg-white rounded-xl shadow-sm border">
            <div class="p-6 border-b border-gray-100">
              <h2 class="text-xl font-semibold text-gray-900">Ringkasan Eksekutif</h2>
              <p class="text-gray-600 mt-1">Gambaran umum profil kekuatan karakter Anda</p>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="executive-summary">
                <!-- Will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </section>

        <!-- Key Insights & Visualization -->
        <section class="mb-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Top Strengths Card -->
            <div class="bg-white rounded-xl shadow-sm border">
              <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Top 5 Character Strengths</h3>
                <p class="text-gray-600 text-sm mt-1">Kekuatan karakter unggulan Anda</p>
              </div>
              <div class="p-6">
                <div class="space-y-4" id="top-strengths-compact">
                  <!-- Will be populated by JavaScript -->
                </div>
              </div>
            </div>

            <!-- Category Overview -->
            <div class="bg-white rounded-xl shadow-sm border">
              <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">Profil Kategori</h3>
                <p class="text-gray-600 text-sm mt-1">Distribusi kekuatan per kategori kebajikan</p>
              </div>
              <div class="p-6">
                <div class="h-64">
                  <canvas id="viaIsCategoriesRadarChart" class="w-full h-full"></canvas>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Detailed Analysis -->
        <section class="mb-8">
          <div class="bg-white rounded-xl shadow-sm border">
            <div class="p-6 border-b border-gray-100">
              <h2 class="text-xl font-semibold text-gray-900">Analisis Mendalam</h2>
              <p class="text-gray-600 mt-1">Eksplorasi detail setiap kategori dan kekuatan karakter</p>
            </div>
            
            <!-- Category Tabs -->
            <div class="border-b border-gray-100">
              <nav class="flex space-x-8 px-6" id="category-tabs">
                <!-- Will be populated by JavaScript -->
              </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6" id="category-content">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>
        </section>

        <!-- Development Recommendations -->
        <section class="mb-8">
          <div class="bg-white rounded-xl shadow-sm border">
            <div class="p-6 border-b border-gray-100">
              <h2 class="text-xl font-semibold text-gray-900">Rekomendasi Pengembangan</h2>
              <p class="text-gray-600 mt-1">Saran praktis untuk mengoptimalkan kekuatan karakter Anda</p>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8" id="development-recommendations">
                <!-- Will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </section>

        <!-- Complete Strengths Overview -->
        <section class="mb-8">
          <div class="bg-white rounded-xl shadow-sm border">
            <div class="p-6 border-b border-gray-100">
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-xl font-semibold text-gray-900">Semua 24 Character Strengths</h2>
                  <p class="text-gray-600 mt-1">Profil lengkap dengan skor detail</p>
                </div>
                <button class="text-indigo-600 hover:text-indigo-700 text-sm font-medium" onclick="toggleFullChart()">
                  <span id="chart-toggle-text">Tampilkan Chart</span>
                </button>
              </div>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 lg:grid-cols-4 gap-4" id="all-strengths-grid">
                <!-- Will be populated by JavaScript -->
              </div>
              <div id="full-chart-container" class="mt-6 hidden">
                <div class="h-96">
                  <canvas id="viaIsStrengthsBarChart" class="w-full h-full"></canvas>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- About VIA-IS -->
        <section class="mb-8">
          <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border p-8">
            <div class="max-w-4xl">
              <h2 class="text-2xl font-bold text-gray-900 mb-6">Tentang VIA-IS Character Strengths</h2>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">Latar Belakang</h3>
                  <div class="space-y-4 text-gray-700">
                    <p>
                      VIA-IS dikembangkan oleh Dr. Christopher Peterson dan Dr. Martin Seligman sebagai 
                      bagian dari gerakan Psikologi Positif untuk mengidentifikasi kekuatan karakter universal.
                    </p>
                    <p>
                      Assessment ini mengukur 24 kekuatan karakter yang terbukti secara ilmiah berkontribusi 
                      pada kesejahteraan, kepuasan hidup, dan kinerja optimal.
                    </p>
                  </div>
                </div>

                <div>
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">Manfaat Aplikasi</h3>
                  <div class="space-y-3">
                    <div class="flex items-start space-x-3">
                      <span class="text-indigo-500 mt-0.5">✓</span>
                      <span class="text-gray-700">Meningkatkan self-awareness dan kepercayaan diri</span>
                    </div>
                    <div class="flex items-start space-x-3">
                      <span class="text-indigo-500 mt-0.5">✓</span>
                      <span class="text-gray-700">Optimalisasi kinerja dalam karir dan tim</span>
                    </div>
                    <div class="flex items-start space-x-3">
                      <span class="text-indigo-500 mt-0.5">✓</span>
                      <span class="text-gray-700">Pengembangan leadership dan komunikasi</span>
                    </div>
                    <div class="flex items-start space-x-3">
                      <span class="text-indigo-500 mt-0.5">✓</span>
                      <span class="text-gray-700">Peningkatan resiliensi dan adaptabilitas</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Navigation Footer -->
        <nav class="flex justify-between items-center">
          <button onclick="navigateTo('result')"
            class="flex items-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Kembali ke Ringkasan
          </button>
          
          <div class="text-center">
            <p class="text-sm text-gray-500 mb-2">Selanjutnya</p>
            <p class="font-medium text-gray-900">RIASEC Interest Assessment</p>
          </div>
          
          <button onclick="navigateTo('result-riasec')"
            class="flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
            Lanjutkan
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          </button>
        </nav>
      </main>
    </div>
  `;
}

export function initResultViaIs() {
  loadViaIsData();
  initializeTabs();
}

function loadViaIsData() {
  try {
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleViaIsData();
    
    console.log('VIA-IS assessment results loaded:', assessmentResults);
    displayViaIsData(assessmentResults.viaIs || assessmentResults);
  } catch (error) {
    console.error('Error loading VIA-IS data:', error);
    displayViaIsData(getSampleViaIsData());
  }
}

function getSampleViaIsData() {
  return {
    creativity: 88, curiosity: 92, judgment: 85, loveOfLearning: 90, perspective: 82,
    bravery: 65, perseverance: 78, honesty: 75, zest: 58,
    love: 62, kindness: 68, socialIntelligence: 55,
    teamwork: 48, fairness: 72, leadership: 65,
    forgiveness: 58, humility: 62, prudence: 85, selfRegulation: 75,
    appreciationOfBeauty: 82, gratitude: 68, hope: 72, humor: 55, spirituality: 45
  };
}

function displayViaIsData(viaIsData) {
  displayExecutiveSummary(viaIsData);
  displayTopStrengths(viaIsData);
  createViaIsCategoriesRadarChart(viaIsData);
  setupCategoryTabs(viaIsData);
  displayDevelopmentRecommendations(viaIsData);
  displayAllStrengthsGrid(viaIsData);
  createViaIsStrengthsBarChart(viaIsData);
}

function displayExecutiveSummary(viaIsData) {
  const container = document.getElementById('executive-summary');
  if (!container) return;

  const categoryAverages = ChartUtils.calculateViaIsCategoryAverages(viaIsData);
  const overallAverage = Math.round(Object.values(categoryAverages).reduce((a, b) => a + b, 0) / Object.keys(categoryAverages).length);
  const topCategory = Object.entries(categoryAverages).reduce((a, b) => a[1] > b[1] ? a : b);
  const strengthsAbove80 = Object.values(viaIsData).filter(score => score >= 80).length;

  container.innerHTML = `
    <div class="text-center p-6 bg-indigo-50 rounded-xl border border-indigo-100">
      <div class="text-3xl font-bold text-indigo-600 mb-2">${overallAverage}%</div>
      <div class="text-sm font-medium text-indigo-800">Overall Score</div>
      <div class="text-xs text-indigo-600 mt-1">Character Strengths</div>
    </div>
    
    <div class="text-center p-6 bg-green-50 rounded-xl border border-green-100">
      <div class="text-3xl font-bold text-green-600 mb-2">${strengthsAbove80}</div>
      <div class="text-sm font-medium text-green-800">Signature Strengths</div>
      <div class="text-xs text-green-600 mt-1">Score ≥ 80%</div>
    </div>
    
    <div class="text-center p-6 bg-purple-50 rounded-xl border border-purple-100">
      <div class="text-lg font-bold text-purple-600 mb-2">${VIA_IS_CATEGORIES[topCategory[0]].name}</div>
      <div class="text-sm font-medium text-purple-800">Dominant Category</div>
      <div class="text-xs text-purple-600 mt-1">${Math.round(topCategory[1])}% Average</div>
    </div>
  `;
}

function displayTopStrengths(viaIsData) {
  const container = document.getElementById('top-strengths-compact');
  if (!container) return;

  const strengthsArray = Object.entries(viaIsData).map(([key, value]) => ({
    name: key,
    score: value,
    displayName: getViaIsDisplayName(key),
    details: VIA_IS_STRENGTH_DETAILS[key]
  }));

  const topStrengths = strengthsArray.sort((a, b) => b.score - a.score).slice(0, 5);

  container.innerHTML = topStrengths.map((strength, index) => {
    const rankColors = ['text-yellow-600', 'text-orange-600', 'text-red-600', 'text-purple-600', 'text-indigo-600'];
    const bgColors = ['bg-yellow-50', 'bg-orange-50', 'bg-red-50', 'bg-purple-50', 'bg-indigo-50'];

    return `
      <div class="flex items-center justify-between p-4 ${bgColors[index]} rounded-lg border">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 ${rankColors[index]} bg-white rounded-full flex items-center justify-center font-bold text-sm">
            ${index + 1}
          </div>
          <div>
            <div class="font-semibold text-gray-900">${strength.displayName}</div>
            <div class="text-xs text-gray-600">${getCategoryForStrength(strength.name)}</div>
          </div>
        </div>
        <div class="text-xl font-bold ${rankColors[index]}">${strength.score}%</div>
      </div>
    `;
  }).join('');
}

function setupCategoryTabs(viaIsData) {
  const tabsContainer = document.getElementById('category-tabs');
  const contentContainer = document.getElementById('category-content');
  if (!tabsContainer || !contentContainer) return;

  const categoryAverages = ChartUtils.calculateViaIsCategoryAverages(viaIsData);
  const sortedCategories = Object.entries(VIA_IS_CATEGORIES)
    .sort((a, b) => categoryAverages[b[0]] - categoryAverages[a[0]]);

  // Create tabs
  tabsContainer.innerHTML = sortedCategories.map(([key, category], index) => `
    <button class="py-4 px-1 border-b-2 font-medium text-sm transition-colors category-tab ${index === 0 ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
      data-category="${key}">
      <div class="flex items-center space-x-2">
        <span>${category.icon}</span>
        <span>${category.name}</span>
        <span class="text-xs bg-gray-100 px-2 py-1 rounded">${Math.round(categoryAverages[key])}%</span>
      </div>
    </button>
  `).join('');

  // Show first category by default
  if (sortedCategories.length > 0) {
    showCategoryContent(sortedCategories[0][0], viaIsData);
  }

  // Add click handlers
  document.querySelectorAll('.category-tab').forEach(tab => {
    tab.addEventListener('click', (e) => {
      // Remove active state from all tabs
      document.querySelectorAll('.category-tab').forEach(t => {
        t.classList.remove('border-indigo-500', 'text-indigo-600');
        t.classList.add('border-transparent', 'text-gray-500');
      });
      
      // Add active state to clicked tab
      tab.classList.remove('border-transparent', 'text-gray-500');
      tab.classList.add('border-indigo-500', 'text-indigo-600');
      
      showCategoryContent(tab.dataset.category, viaIsData);
    });
  });
}

function showCategoryContent(categoryKey, viaIsData) {
  const container = document.getElementById('category-content');
  if (!container) return;

  const category = VIA_IS_CATEGORIES[categoryKey];
  const categoryAverages = ChartUtils.calculateViaIsCategoryAverages(viaIsData);
  const strengths = category.strengths.map(strength => ({
    name: strength,
    score: viaIsData[strength] || 0,
    displayName: getViaIsDisplayName(strength),
    details: VIA_IS_STRENGTH_DETAILS[strength]
  })).sort((a, b) => b.score - a.score);

  container.innerHTML = `
    <div class="mb-6">
      <div class="flex items-start space-x-4">
        <div class="text-4xl">${category.icon}</div>
        <div class="flex-1">
          <h3 class="text-xl font-semibold text-gray-900 mb-2">${category.name}</h3>
          <p class="text-gray-600 mb-4">${category.description}</p>
          <div class="flex items-center space-x-4">
            <div class="text-2xl font-bold text-indigo-600">${Math.round(categoryAverages[categoryKey])}%</div>
            <div class="flex-1">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-indigo-600 h-2 rounded-full transition-all duration-500" 
                  style="width: ${categoryAverages[categoryKey]}%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      ${strengths.map(strength => {
        const level = strength.score >= 80 ? 'high' : strength.score >= 60 ? 'medium' : 'low';
        const levelClasses = {
          high: 'bg-green-50 border-green-200 text-green-800',
          medium: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          low: 'bg-blue-50 border-blue-200 text-blue-800'
        };
        const scoreClasses = {
          high: 'text-green-600',
          medium: 'text-yellow-600',
          low: 'text-blue-600'
        };

        return `
          <div class="border rounded-lg p-4 ${levelClasses[level]}">
            <div class="flex justify-between items-center mb-3">
              <h4 class="font-semibold">${strength.displayName}</h4>
              <span class="text-xl font-bold ${scoreClasses[level]}">${strength.score}%</span>
            </div>
            <p class="text-sm opacity-90 leading-relaxed mb-3">${strength.details?.description || ''}</p>
            <div class="w-full bg-white bg-opacity-50 rounded-full h-2">
              <div class="${scoreClasses[level].replace('text-', 'bg-')} h-2 rounded-full transition-all duration-500" 
                style="width: ${strength.score}%"></div>
            </div>
          </div>
        `;
      }).join('')}
    </div>
  `;
}

function displayDevelopmentRecommendations(viaIsData) {
  const container = document.getElementById('development-recommendations');
  if (!container) return;

  const allStrengths = Object.entries(viaIsData).map(([key, value]) => ({
    name: key,
    score: value,
    displayName: getViaIsDisplayName(key)
  }));

  const topStrengths = allStrengths.sort((a, b) => b.score - a.score).slice(0, 3);
  const developmentAreas = allStrengths.sort((a, b) => a.score - b.score).slice(0, 3);

  container.innerHTML = `
    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
      <h3 class="font-semibold text-green-800 mb-4 flex items-center">
        <span class="text-green-500 mr-2">🎯</span>
        Leverage Your Strengths
      </h3>
      <p class="text-green-700 text-sm mb-4">
        Maksimalkan kekuatan utama Anda dalam aktivitas sehari-hari:
      </p>
      <div class="space-y-3">
        ${topStrengths.map(strength => `
          <div class="bg-white rounded p-3">
            <div class="font-medium text-green-800 mb-1">${strength.displayName} (${strength.score}%)</div>
            <div class="text-sm text-green-700">
              ${getStrengthRecommendation(strength.name, 'leverage')}
            </div>
          </div>
        `).join('')}
      </div>
    </div>

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <h3 class="font-semibold text-blue-800 mb-4 flex items-center">
        <span class="text-blue-500 mr-2">📈</span>
        Development Opportunities
      </h3>
      <p class="text-blue-700 text-sm mb-4">
        Area yang dapat dikembangkan untuk profil yang lebih seimbang:
      </p>
      <div class="space-y-3">
        ${developmentAreas.map(strength => `
          <div class="bg-white rounded p-3">
            <div class="font-medium text-blue-800 mb-1">${strength.displayName} (${strength.score}%)</div>
            <div class="text-sm text-blue-700">
              ${getStrengthRecommendation(strength.name, 'develop')}
            </div>
          </div>
        `).join('')}
      </div>
    </div>
  `;
}

function displayAllStrengthsGrid(viaIsData) {
  const container = document.getElementById('all-strengths-grid');
  if (!container) return;

  const allStrengths = Object.entries(viaIsData).map(([key, value]) => ({
    name: key,
    score: value,
    displayName: getViaIsDisplayName(key),
    category: getCategoryForStrength(key)
  })).sort((a, b) => b.score - a.score);

  container.innerHTML = allStrengths.map(strength => {
    const level = strength.score >= 80 ? 'high' : strength.score >= 60 ? 'medium' : 'low';
    const levelClasses = {
      high: 'bg-green-50 border-green-200',
      medium: 'bg-yellow-50 border-yellow-200', 
      low: 'bg-blue-50 border-blue-200'
    };
    const scoreClasses = {
      high: 'text-green-600',
      medium: 'text-yellow-600',
      low: 'text-blue-600'
    };

    return `
      <div class="border rounded-lg p-4 ${levelClasses[level]} hover:shadow-md transition-shadow">
        <div class="flex justify-between items-center mb-2">
          <div class="font-medium text-gray-900 text-sm">${strength.displayName}</div>
          <div class="text-lg font-bold ${scoreClasses[level]}">${strength.score}%</div>
        </div>
        <div class="text-xs text-gray-600 mb-2">${strength.category}</div>
        <div class="w-full bg-white bg-opacity-50 rounded-full h-1.5">
          <div class="${scoreClasses[level].replace('text-', 'bg-')} h-1.5 rounded-full transition-all duration-300" 
            style="width: ${strength.score}%"></div>
        </div>
      </div>
    `;
  }).join('');
}

function createViaIsCategoriesRadarChart(viaIsData) {
  const ctx = document.getElementById('viaIsCategoriesRadarChart');
  if (!ctx) return;
  return ChartUtils.createViaIsCategoriesRadarChart(ctx, viaIsData);
}

function createViaIsStrengthsBarChart(viaIsData) {
  const ctx = document.getElementById('viaIsStrengthsBarChart');
  if (!ctx) return;
  return ChartUtils.createViaIsStrengthsBarChart(ctx, viaIsData);
}

function initializeTabs() {
  // Tab functionality is handled in setupCategoryTabs
}

// Toggle full chart visibility
window.toggleFullChart = function() {
  const container = document.getElementById('full-chart-container');
  const toggleText = document.getElementById('chart-toggle-text');
  
  if (container.classList.contains('hidden')) {
    container.classList.remove('hidden');
    toggleText.textContent = 'Sembunyikan Chart';
  } else {
    container.classList.add('hidden');
    toggleText.textContent = 'Tampilkan Chart';
  }
};

function getViaIsDisplayName(key) {
  const displayNames = {
    creativity: 'Kreativitas',
    curiosity: 'Keingintahuan', 
    judgment: 'Penilaian',
    loveOfLearning: 'Cinta Belajar',
    perspective: 'Perspektif',
    bravery: 'Keberanian',
    perseverance: 'Ketekunan',
    honesty: 'Kejujuran',
    zest: 'Semangat',
    love: 'Cinta',
    kindness: 'Kebaikan',
    socialIntelligence: 'Kecerdasan Sosial',
    teamwork: 'Kerja Tim',
    fairness: 'Keadilan',
    leadership: 'Kepemimpinan',
    forgiveness: 'Pengampunan',
    humility: 'Kerendahan Hati',
    prudence: 'Kehati-hatian',
    selfRegulation: 'Pengaturan Diri',
    appreciationOfBeauty: 'Apresiasi Keindahan',
    gratitude: 'Rasa Syukur',
    hope: 'Harapan',
    humor: 'Humor',
    spirituality: 'Spiritualitas'
  };
  return displayNames[key] || key;
}

function getCategoryForStrength(strengthKey) {
  for (const [categoryKey, category] of Object.entries(VIA_IS_CATEGORIES)) {
    if (category.strengths.includes(strengthKey)) {
      return VIA_IS_CATEGORIES[categoryKey].name;
    }
  }
  return 'Unknown';
}

function getStrengthRecommendation(strengthKey, type) {
  const recommendations = {
    leverage: {
      creativity: 'Gunakan dalam brainstorming, problem-solving, dan proyek inovatif.',
      curiosity: 'Explore topik baru, ajukan pertanyaan mendalam, dan pelajari skill baru.',
      judgment: 'Berperan dalam pengambilan keputusan tim dan analisis situasi kompleks.',
      loveOfLearning: 'Jadilah mentor, ikuti kursus, dan bagikan pengetahuan dengan others.',
      perspective: 'Berikan insight dan wisdom dalam diskusi, jadi advisor untuk others.',
      bravery: 'Ambil inisiatif dalam situasi sulit, lead change, dan advocacy.',
      perseverance: 'Handle proyek jangka panjang dan tantangan yang membutuhkan konsistensi.',
      honesty: 'Jadi role model integrity, berikan feedback jujur dan constructive.',
      kindness: 'Fokus pada people development, mentoring, dan community service.',
      leadership: 'Ambil peran kepemimpinan dalam proyek dan inisiatif organisasi.'
    },
    develop: {
      teamwork: 'Join collaborative projects, praktikkan active listening, dan support team goals.',
      socialIntelligence: 'Perhatikan non-verbal cues, praktikkan empathy, dan improve communication.',
      humor: 'Gunakan appropriate humor untuk lighten situations dan build rapport.',
      forgiveness: 'Praktikkan letting go, fokus pada solutions rather than blame.',
      humility: 'Acknowledge contributions others, minta feedback, dan stay open-minded.',
      spirituality: 'Explore meaning dan purpose, praktikkan mindfulness atau meditation.',
      zest: 'Find activities yang energizing, set exciting goals, dan celebrate achievements.',
      love: 'Invest dalam relationships, show appreciation, dan build deeper connections.'
    }
  };

  return recommendations[type][strengthKey] || `Focus on developing this strength through daily practice and conscious application.`;
}